import pandas as pd
import sqlite3
from datetime import datetime
import numpy as np
import matplotlib.pyplot as plt

from backtest import RISK_FREE_RATE

# Connect to databases

db_name = 'cotacoes copy.db'

TOTAL_VALUE = 25000
VALUE_PER_ASSET = 5000
OFFER_SPREAD = 0.1
RISK_FREE_RATE = 0.1

def get_trade_system_data(window_size=10, n_liquidity_days = 90, n_liquidity_assets = 50, n_selected_assets = 10):
    """
    Retrieves trade system dataframes:
    - df_assets_names: asset names
    - df_close_d_minus_2: close prices from D-2
    - df_close_d_minus_1: close prices from D-1
    - df_open_d_plus_0: open prices from D+0
    - moving_sum: the rolling sum of signals

    :param window_size: The rolling window size for the moving sum
    :return: pandas DataFrame with the requested columns
    """
    conn = sqlite3.connect(db_name)

    # Single consolidated SQL query
    query = f"""
    WITH liquidity_ranked AS (
        SELECT
            c_base.date,
            c_base.symbol,
            SUM(c_window.close * c_window.volume) AS total_financial_volume_rolling_days,
            ROW_NUMBER() OVER (PARTITION BY c_base.date ORDER BY SUM(c_window.close * c_window.volume) DESC) AS rn
        FROM
            cotacoes AS c_base
        JOIN
            cotacoes AS c_window ON c_window.symbol = c_base.symbol
            AND c_window.date BETWEEN DATE(c_base.date, '-' || ({n_liquidity_days} - 1) || ' day') AND c_base.date
        WHERE
            c_base.close * c_base.volume IS NOT NULL
            AND c_window.close * c_window.volume IS NOT NULL
        GROUP BY
            c_base.date, c_base.symbol
    ),
    thresholds AS (
        SELECT
            date,
            MIN(total_financial_volume_rolling_days) AS liquidity_volume_threshold
        FROM
            liquidity_ranked
        WHERE
            rn = {n_liquidity_assets}
        GROUP BY
            date
    ),
    asset_volumes AS (
        SELECT
            c.date,
            c.symbol,
            SUM(cw.close * cw.volume) AS total_financial_volume_rolling_days
        FROM
            cotacoes AS c
        JOIN
            cotacoes AS cw ON cw.symbol = c.symbol
            AND cw.date BETWEEN DATE(c.date, '-' || ({n_liquidity_days} - 1) || ' day') AND c.date
        WHERE
            c.close * c.volume IS NOT NULL
            AND cw.close * cw.volume IS NOT NULL
        GROUP BY
            c.date, c.symbol
    ),
    assets_above_threshold AS (
        SELECT
            a.date,
            a.symbol
        FROM
            asset_volumes AS a
        JOIN
            thresholds AS t ON a.date = t.date
        WHERE
            a.total_financial_volume_rolling_days >= t.liquidity_volume_threshold
    ),
    prices_with_lags AS (
        SELECT
            c.symbol,
            c.date,
            c.open,
            c.close,
            LAG(c.close, 1) OVER (PARTITION BY c.symbol ORDER BY c.date) AS close_d_minus_1,
            LAG(c.close, 2) OVER (PARTITION BY c.symbol ORDER BY c.date) AS close_d_minus_2
        FROM
            cotacoes AS c
    ),
    signals AS (
        SELECT
            p.symbol,
            p.date,
            p.open,
            p.close,
            p.close_d_minus_1,
            p.close_d_minus_2,
            CASE
                WHEN p.open > p.close_d_minus_1 THEN 1
                WHEN p.open < p.close_d_minus_1 THEN -1
                ELSE 0
            END AS signal
        FROM
            prices_with_lags AS p
    ),
    moving_sums AS (
        SELECT
            s.*,
            SUM(signal) OVER (
                PARTITION BY s.symbol
                ORDER BY s.date
                ROWS BETWEEN {window_size - 1} PRECEDING AND CURRENT ROW
            ) AS moving_sum
        FROM
            signals AS s
    ),
    selected_assets AS (
        SELECT
            m.date,
            m.symbol,
            m.close_d_minus_2,
            m.close_d_minus_1,
            m.open AS open_d_plus_0,
            m.moving_sum,
            ROW_NUMBER() OVER (PARTITION BY m.date ORDER BY m.moving_sum DESC) AS rn
        FROM
            moving_sums AS m
        JOIN
            assets_above_threshold AS a ON m.symbol = a.symbol AND m.date = a.date
    ),
    top_assets AS (
        SELECT
            *
        FROM
            selected_assets
        WHERE
            rn <= {n_selected_assets}
    )
    SELECT
        date,
        symbol,
        close_d_minus_2,
        close_d_minus_1,
        open_d_plus_0,
        moving_sum
    FROM
        top_assets
    ORDER BY
        date, symbol;
    """

    # Execute query and load results into a DataFrame
    df = pd.read_sql_query(query, conn, parse_dates=['date'])

    # Close DB connection
    conn.close()

    return df.sort_values(by=["moving_sum", "date"], ascending=[False, True]).copy()

def get_open_price_from_db(symbol, date):
    conn = sqlite3.connect(db_name)
    query = f"""
        SELECT open
        FROM cotacoes
        WHERE symbol = '{symbol}' AND date = '{date}'
        LIMIT 1
    """
    df = pd.read_sql_query(query, conn)
    conn.close()
    if not df.empty:
        return df.loc[0, 'open']
    else:
        return np.nan

def analyze_trade_system(trades, initial_capital=TOTAL_VALUE, risk_free_rate=0.02):
    """
    Performs a comprehensive analysis of the trading system's performance.

    :param trades: A dictionary of completed trades with 'profit' and 'date' (buy date for simplicity,
                   ideally would be sell date for realized profit plotting).
    :param initial_capital: The starting capital of the trading system.
    :param risk_free_rate: Annual risk-free rate for Sharpe Ratio calculation (e.g., bond yield).
    """
    if not trades:
        print("No trades available for analysis.")
        return

    print("\n--- Trade System Analysis ---")

    # Convert trades to DataFrame for easier processing
    trade_list = []
    for (symbol, buy_date), trade_details in trades.items():
        trade_list.append({
            'symbol': symbol,
            'buy_date': pd.to_datetime(buy_date), # Use buy_date from key
            'profit': trade_details['profit'],
            'buy_price': trade_details['buy_price'],
            'sell_price': trade_details['sell_price'],
            'quantity': trade_details['quantity']
        })
    df_trades = pd.DataFrame(trade_list)

    if df_trades.empty:
        print("No valid trades to analyze.")
        return

    # --- 1. Basic Performance Metrics ---
    total_trades = len(df_trades)
    winning_trades = df_trades[df_trades['profit'] > 0]
    losing_trades = df_trades[df_trades['profit'] < 0]
    num_winning_trades = len(winning_trades)
    num_losing_trades = len(losing_trades)
    total_profit_loss = df_trades['profit'].sum()
    final_capital = initial_capital + total_profit_loss

    win_rate = (num_winning_trades / total_trades) * 100 if total_trades > 0 else 0
    average_win = winning_trades['profit'].mean() if num_winning_trades > 0 else 0
    average_loss = losing_trades['profit'].mean() if num_losing_trades > 0 else 0 # Will be negative

    print(f"Total Trades: {total_trades}")
    print(f"Winning Trades: {num_winning_trades}")
    print(f"Losing Trades: {num_losing_trades}")
    print(f"Win Rate: {win_rate:.2f}%")
    print(f"Total Net Profit/Loss: ${total_profit_loss:.2f}")
    print(f"Initial Capital: ${initial_capital:.2f}")
    print(f"Final Capital: ${final_capital:.2f}")
    print(f"Absolute Return: {((final_capital - initial_capital) / initial_capital * 100):.2f}%")
    print(f"Average Winning Trade: ${average_win:.2f}")
    print(f"Average Losing Trade: ${average_loss:.2f}")

    if average_loss != 0: # Avoid division by zero
        risk_reward_ratio = abs(average_win / average_loss)
        print(f"Average Risk/Reward Ratio (Avg Win / Avg Loss Magnitude): {risk_reward_ratio:.2f}")
    else:
        print("Average Risk/Reward Ratio: N/A (no losing trades)")

    # --- 2. Equity Curve and Drawdown Analysis ---
    # For drawdown, we need a time series of portfolio values.
    # We'll use the 'buy_date' from the trade key as the date for profit realization.
    # A more robust solution might use the 'sell_date' from trade_details if available.
    df_equity = df_trades[['buy_date', 'profit']].copy()
    df_equity.columns = ['date', 'profit'] # Rename for clarity
    df_equity = df_equity.sort_values(by='date')
    df_equity['cumulative_profit'] = df_equity['profit'].cumsum()
    df_equity['portfolio_value'] = initial_capital + df_equity['cumulative_profit']

    # Calculate Max Drawdown
    df_equity['peak'] = df_equity['portfolio_value'].cummax()
    df_equity['drawdown'] = (df_equity['portfolio_value'] - df_equity['peak']) / df_equity['peak']
    max_drawdown = df_equity['drawdown'].min() * 100 if not df_equity.empty else 0

    print(f"Maximum Drawdown: {max_drawdown:.2f}%")

    # --- 3. Risk-Adjusted Returns (Sharpe Ratio) ---
    # Requires daily returns or period returns and risk-free rate.
    # For simplicity, let's convert to daily returns.
    # Note: This assumes profits are realized on the 'buy_date' and uses that for daily aggregation.
    # If trades span multiple days, this might need refinement (e.g., allocating profit over trade duration).
    df_daily_returns = df_equity.set_index('date')['portfolio_value'].resample('D').last().ffill().diff().fillna(0)
    df_daily_returns = df_daily_returns[df_daily_returns != 0] # Filter out days with no change

    if not df_daily_returns.empty:
        # Convert total profit to daily returns for Sharpe ratio calculation
        # This is a simplified approach. A more accurate Sharpe would use percentage daily returns.
        daily_returns_percentage = df_daily_returns / df_equity.set_index('date')['portfolio_value'].shift(1).resample('D').last().ffill()
        daily_returns_percentage.fillna(0, inplace=True) # Fill NaN from first day

        avg_daily_return = daily_returns_percentage.mean()
        std_daily_return = daily_returns_percentage.std()

        # Annualize the risk-free rate to daily
        daily_risk_free_rate = (1 + risk_free_rate)**(1/252) - 1 # Assuming 252 trading days a year

        if std_daily_return != 0:
            sharpe_ratio = (avg_daily_return - daily_risk_free_rate) / std_daily_return * np.sqrt(252) # Annualize
            print(f"Sharpe Ratio (Annualized): {sharpe_ratio:.2f}")
        else:
            print("Sharpe Ratio: N/A (No volatility in returns)")
    else:
        print("Sharpe Ratio: N/A (Not enough data for daily returns)")
    #
    # # --- 4. Plotting the Equity Curve and Drawdown ---
    # fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10), sharex=True)
    #
    # # Equity Curve
    # ax1.plot(df_equity['date'], df_equity['portfolio_value'], label='Portfolio Value', color='blue')
    # ax1.set_title('Equity Curve (Portfolio Value Over Time)')
    # ax1.set_ylabel('Portfolio Value ($)')
    # ax1.grid(True)
    # ax1.legend()
    #
    # # Drawdown Curve
    # ax2.plot(df_equity['date'], df_equity['drawdown'] * 100, label='Drawdown (%)', color='red')
    # ax2.fill_between(df_equity['date'], df_equity['drawdown'] * 100, 0, color='red', alpha=0.3)
    # ax2.set_title('Drawdown Over Time')
    # ax2.set_xlabel('Date')
    # ax2.set_ylabel('Drawdown (%)')
    # ax2.grid(True)
    # ax2.axhline(0, color='grey', linestyle='--', linewidth=0.8)
    # ax2.legend()
    #
    # plt.tight_layout()
    # plt.show()



df = get_trade_system_data(window_size=10, n_liquidity_days = 90,n_liquidity_assets = 50, n_selected_assets = 10)
available_capital = TOTAL_VALUE
open_positions = {}
trades = {}
for date, daily_assets in df.groupby('date'):
    for idx, asset in daily_assets.iterrows():
        if asset['symbol'] in open_positions or available_capital <= VALUE_PER_ASSET:
            continue  # skip if already open
        else:
            if np.isnan(asset['close_d_minus_2']):
                continue  # cannot buy
            asset_quantity = int(VALUE_PER_ASSET / (asset['close_d_minus_2'] * 100)) * 100
            if asset_quantity == 0:
                continue  # cannot buy
            order_value = asset['close_d_minus_2'] * (1 + OFFER_SPREAD)
            if order_value >= asset['close_d_minus_1']:
                open_positions[asset['symbol']] = {
                    'quantity': asset_quantity,
                    'buy_price': asset['close_d_minus_1'],
                    'sell_price': asset['open_d_plus_0'],
                    'date': date
                }
                available_capital -= asset_quantity * asset['close_d_minus_1']


    for symbol, position in open_positions.copy().items():
        sell_price = position['sell_price']
        if np.isnan(sell_price) and date == position['date']:
            continue  # cannot close
        elif np.isnan(sell_price):
            sell_price = get_open_price_from_db(symbol, date)
            if np.isnan(sell_price):
                continue  # cannot close
        available_capital += position['quantity'] * sell_price
        trades[(symbol, position['date'])] = {
            'symbol': symbol,
            'quantity': position['quantity'],
            'buy_price': position['buy_price'],
            'sell_price': sell_price,
            'profit': (sell_price - position['buy_price']) * position['quantity']
        }
        open_positions.pop(symbol)

if len(open_positions) > 0:
    print(f"open_positions: {open_positions}")

analyze_trade_system(trades, initial_capital=TOTAL_VALUE, risk_free_rate=RISK_FREE_RATE)
